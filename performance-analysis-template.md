# 网站性能数据收集与分析表格

**网站地址**: http://localhost:5173  
**测试日期**: 2025年9月5日 
**测试环境**: ___________  
**浏览器版本**: ___________  

## 1. 页面加载性能指标 (Core Web Vitals & Loading Metrics)

| 指标名称 | 测试值 | 理想值 | 说明 |
|---------|--------|--------|------|
| **FCP (First Contentful Paint)** | _____ ms | < 1800ms | 首次内容绘制时间，页面开始显示内容的时间 |
| **LCP (Largest Contentful Paint)** | 0.47s | < 2500ms | 最大内容绘制时间，页面主要内容完全加载的时间 |
| **CLS (Cumulative Layout Shift)** | 0 | < 0.1 | 累积布局偏移，衡量页面视觉稳定性 |
| **FID (First Input Delay)** | _____ ms | < 100ms | 首次输入延迟，用户首次交互到浏览器响应的时间 |
| **INP (Interaction to Next Paint)** | _____ ms | < 200ms | 交互到下次绘制时间，衡量页面响应性 |
| **TTFB (Time to First Byte)** | _____ ms | < 800ms | 首字节时间，服务器响应时间 |
| **Speed Index** | _____ | < 3400 | 速度指数，页面内容填充速度 |
| **Total Blocking Time** | _____ ms | < 200ms | 总阻塞时间，主线程被阻塞的时间 |

## 2. 网络请求性能数据 (Network Performance)

### 2.1 整体网络指标
| 指标名称 | 测试值 | 说明 |
|---------|--------|------|
| **总请求数量** | 60 个 | 页面加载过程中的HTTP请求总数 |
| **总传输大小** | 9.5 MB | 实际网络传输的数据大小 |
| **总资源大小** | 9.4 MB | 解压后的资源总大小 |
| **DOMContentLoaded** | 467 ms | DOM构建完成时间 |
| **Load Event** | 47 ms | 所有资源加载完成时间 |
| **Finish Time** | _____ ms | 最后一个请求完成时间 |

### 2.2 关键资源加载时间
| 资源类型 | 文件名/URL | 大小 | 加载时间 | 说明 |
|---------|-----------|------|---------|------|
| **HTML文档** | index.html | _____ KB | _____ ms | 主HTML文档 |
| **主要JS文件** | _____ | _____ KB | _____ ms | Vue应用主文件 |
| **主要CSS文件** | _____ | _____ KB | _____ ms | 样式文件 |
| **字体文件** | _____ | _____ KB | _____ ms | 自定义字体 |
| **图片资源** | _____ | _____ KB | _____ ms | 关键图片 |
| **API请求** | _____ | _____ KB | _____ ms | 数据接口 |

## 3. JavaScript执行性能 (JavaScript Performance)

| 指标名称 | 测试值 | 说明 |
|---------|--------|------|
| **脚本解析时间** | _____ ms | JavaScript文件解析耗时 |
| **脚本执行时间** | _____ ms | JavaScript代码执行耗时 |
| **主线程工作时间** | _____ ms | 主线程总工作时间 |
| **长任务数量** | _____ 个 | 超过50ms的长任务数量 |
| **最长任务时间** | _____ ms | 单个最长任务的执行时间 |
| **Vue组件渲染时间** | _____ ms | Vue组件初始渲染时间 |
| **ECharts初始化时间** | _____ ms | 图表库初始化时间 |

## 4. 内存使用情况 (Memory Usage)

| 指标名称 | 测试值 | 说明 |
|---------|--------|------|
| **JS堆大小** | _____ MB | JavaScript堆内存使用量 |
| **JS堆限制** | _____ MB | JavaScript堆内存限制 |
| **已使用JS堆** | _____ MB | 实际使用的JS堆内存 |
| **DOM节点数量** | _____ 个 | 页面DOM节点总数 |
| **监听器数量** | _____ 个 | 事件监听器数量 |
| **GPU内存使用** | _____ MB | GPU内存使用量（如有） |

## 5. 用户交互响应时间 (User Interaction)

| 交互类型 | 测试值 | 理想值 | 说明 |
|---------|--------|--------|------|
| **按钮点击响应** | _____ ms | < 100ms | 按钮点击到视觉反馈的时间 |
| **表单输入响应** | _____ ms | < 50ms | 输入框输入到显示的延迟 |
| **页面滚动响应** | _____ fps | 60fps | 滚动时的帧率 |
| **图表交互响应** | _____ ms | < 100ms | ECharts图表交互响应时间 |
| **路由切换时间** | _____ ms | < 200ms | Vue Router页面切换时间 |
| **数据刷新时间** | _____ ms | < 500ms | 数据更新到界面刷新的时间 |

## 6. Lighthouse性能评分 (Lighthouse Scores)

| 评分类别 | 得分 | 说明 |
|---------|------|------|
| **Performance** | _____ /100 | 性能评分 |
| **Accessibility** | _____ /100 | 可访问性评分 |
| **Best Practices** | _____ /100 | 最佳实践评分 |
| **SEO** | _____ /100 | SEO评分 |
| **PWA** | _____ /100 | PWA评分（如适用） |

### Lighthouse性能诊断
| 诊断项目 | 状态 | 建议 |
|---------|------|------|
| **未使用的JavaScript** | _____ KB | _____ |
| **未使用的CSS** | _____ KB | _____ |
| **图片优化机会** | _____ KB | _____ |
| **文本压缩机会** | _____ KB | _____ |
| **关键请求链** | _____ 层 | _____ |

## 7. 移动端性能数据 (Mobile Performance)

| 指标名称 | 测试值 | 说明 |
|---------|--------|------|
| **移动端LCP** | _____ ms | 移动设备上的LCP时间 |
| **移动端FCP** | _____ ms | 移动设备上的FCP时间 |
| **移动端CLS** | _____ | 移动设备上的CLS值 |
| **移动端Performance评分** | _____ /100 | Lighthouse移动端性能评分 |

---

## 数据收集说明

### Chrome DevTools使用方法：
1. **Performance面板**: F12 → Performance → 点击录制按钮 → 刷新页面 → 停止录制
2. **Network面板**: F12 → Network → 刷新页面 → 查看请求列表
3. **Lighthouse**: F12 → Lighthouse → Generate report
4. **Memory面板**: F12 → Memory → 查看堆快照

### 测试建议：
- 使用无痕模式测试，避免扩展程序影响
- 清除缓存后进行测试
- 多次测试取平均值
- 分别测试桌面端和移动端
- 在不同网络条件下测试（Fast 3G、Slow 3G等）

---

**填表完成后，请将数据发送给我进行详细的性能分析和优化建议！**
