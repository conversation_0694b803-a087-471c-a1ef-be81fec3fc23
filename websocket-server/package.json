{"name": "websocket-server", "version": "1.0.0", "description": "A WebSocket server for simulating IoT device data.", "main": "src/server.ts", "scripts": {"start": "ts-node src/server.ts", "build": "tsc", "test": "echo \"No tests specified\" && exit 0", "init-db": "ts-node src/database/init.ts"}, "dependencies": {"ws": "^8.0.0", "sqlite3": "^5.1.6", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"@types/ws": "^8.18.1", "@types/sqlite3": "^3.1.8", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "ts-node": "^10.0.0", "typescript": "^4.0.0"}, "author": "", "license": "ISC"}