请帮我创建一个交互式的工厂车间地图组件，具体要求如下：

## 技术实现要求
- 使用现代前端技术栈（React/Vue/原生JavaScript）创建
- 采用SVG或Canvas进行2D渲染
- 响应式设计，适配不同屏幕尺寸

## 地图布局设计
- **视图类型**：单层平面俯视图，无需3D或多楼层
- **区域划分**：清晰标示3-5个功能区域（如：生产区、仓储区、办公区、检测区）
- **尺寸规模**：模拟小型工厂（建议800x600像素的画布区域）
- **视觉风格**：简洁的工业风格，使用灰色调为主，区域用不同颜色区分

## 设备标注系统
- **设备数量**：15-25个设备点位（符合小规模工厂）
- **标注样式**：使用圆形或方形图标标记设备位置
- **状态显示**：
  - 在线状态：绿色图标
  - 离线状态：红色或灰色图标
- **位置编码**：每个设备有唯一的位置标识（格式：X区Y排，如"1区3排"）

## 交互功能
- **点击事件**：点击设备图标弹出信息卡片
- **信息展示**：设备名称、具体位置、当前状态、可选的其他参数
- **缩放平移**：支持地图的缩放和拖拽浏览（可选）

## 数据结构
- 提供示例设备数据（JSON格式）
- 包含设备ID、名称、坐标位置、状态、所属区域等字段

## 交付内容
- 完整的HTML/CSS/JavaScript代码
- 可直接在浏览器中运行的演示
- 简单的使用说明文档

请基于当前项目的技术栈和文件结构来实现这个地图组件。